import React from 'react'
import { motion } from 'framer-motion'
import { Shield, Lock, ChevronRight } from 'lucide-react'
import { Button } from '@/components/ui/button'

const HeroSection = () => {
  return (
    <section id='home' className='pt-20 pb-16 relative overflow-hidden'>
      <div className='absolute inset-0 bg-gradient-to-br from-blue-600/10 via-cyan-500/5 to-blue-400/10'></div>
      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative'>
        <div className='grid lg:grid-cols-2 gap-12 items-center min-h-[80vh]'>
          <motion.div
            className='space-y-8'
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
          >
            <div className='space-y-4'>
              <h1 className='text-5xl lg:text-6xl font-bold leading-tight'>
                <span className='bg-gradient-to-r from-blue-600 via-cyan-500 to-blue-700 bg-clip-text text-transparent'>
                  浙江极安信息科技
                </span>
                <br />
                <span className='text-gray-800'>有限公司</span>
              </h1>
              <p className='text-xl text-gray-600 leading-relaxed'>攻防一体守护数字时代安全</p>
            </div>

            <div className='flex flex-col sm:flex-row gap-4'>
              <Button
                size='lg'
                className='bg-gradient-to-r from-blue-600 to-cyan-500 hover:from-blue-700 hover:to-cyan-600 text-lg px-8 py-4'
              >
                了解更多
                <ChevronRight className='ml-2 w-5 h-5' />
              </Button>
              <Button
                size='lg'
                variant='outline'
                className='border-blue-300 text-blue-600 hover:bg-blue-50 text-lg px-8 py-4'
              >
                联系咨询
              </Button>
            </div>

            <div className='grid grid-cols-3 gap-8 pt-8'>
              <div className='text-center'>
                <div className='text-3xl font-bold text-blue-600'>2023</div>
                <div className='text-sm text-gray-600'>成立年份</div>
              </div>
              <div className='text-center'>
                <div className='text-3xl font-bold text-cyan-500'>100+</div>
                <div className='text-sm text-gray-600'>服务客户</div>
              </div>
              <div className='text-center'>
                <div className='text-3xl font-bold text-blue-600'>24/7</div>
                <div className='text-sm text-gray-600'>安全保障</div>
              </div>
            </div>
          </motion.div>

          <motion.div
            className='relative'
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <div className='relative'>
              <img
                src='/public/slides/slide-09.png'
                alt='极安科技-安全风险实时发现'
                className='w-full h-auto rounded-2xl shadow-2xl'
              />
              <div className='absolute inset-0 bg-gradient-to-tr from-blue-600/20 to-transparent rounded-2xl'></div>
            </div>

            <motion.div
              className='absolute -top-4 -right-4 w-20 h-20 bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full flex items-center justify-center shadow-lg'
              animate={{ y: [-10, 10, -10] }}
              transition={{ duration: 3, repeat: Infinity }}
            >
              <Shield className='w-10 h-10 text-white' />
            </motion.div>

            <motion.div
              className='absolute -bottom-4 -left-4 w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-400 rounded-full flex items-center justify-center shadow-lg'
              animate={{ y: [10, -10, 10] }}
              transition={{ duration: 3, repeat: Infinity, delay: 1 }}
            >
              <Lock className='w-8 h-8 text-white' />
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default HeroSection
