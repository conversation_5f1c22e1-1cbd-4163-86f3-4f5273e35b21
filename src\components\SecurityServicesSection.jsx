import React from 'react'
import { motion } from 'framer-motion'
import { Eye, Users, Server } from 'lucide-react'

const SecurityServicesSection = ({ securityServices, fadeInUp, staggerContainer }) => {
  const serviceDetails = [
    {
      title: '渗透测试',
      description:
        '渗透测试注重广度，以信息系统为单位，利用已知漏洞信息对核心业务系统进行非破坏性模拟攻击，发现漏洞后协助修复并重复，直到漏洞完全修复。',
      icon: Eye
    },
    {
      title: '红队评估',
      description:
        '红队评估注重深度，以实体目标为单位，针对人员、软件、硬件和设备进行的多层次对抗性攻击模拟，以发现技术、人员和基础架构中的漏洞，模拟真实黑客攻击来识别潜在安全风险。',
      icon: Users
    },
    {
      title: '代码安全检测',
      description:
        '从源代码层面对系统进行深层次的安全审计检查，检查代码编写中可能存在的编写不规范、编码错误、验证缺失等代码安全问题，确保在系统上线运行前就解决系统漏洞。',
      icon: Server
    }
  ]

  return (
    <section id='services' className='py-20 bg-white'>
      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
        <motion.div
          className='text-center mb-16'
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className='text-4xl font-bold mb-4'>
            <span className='bg-gradient-to-r from-blue-600 to-cyan-500 bg-clip-text text-transparent'>
              安全服务体系
            </span>
          </h2>
          <p className='text-lg text-gray-600 max-w-3xl mx-auto'>
            极安科技以实战、实网、对抗、常态为目标，为客户提供基于边界安全、内网安全、安全保障、安全合规、安全意识与培训、安全监管赋能等六大服务体系的全面安全服务。
          </p>
        </motion.div>

        <motion.div
          className='grid lg:grid-cols-3 xl:grid-cols-6 gap-6'
          variants={staggerContainer}
          initial='initial'
          whileInView='animate'
          viewport={{ once: true }}
        >
          {securityServices.map((category, categoryIndex) => (
            <motion.div
              key={categoryIndex}
              className='bg-gradient-to-br from-blue-50 to-cyan-50 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300'
              variants={fadeInUp}
              whileHover={{ scale: 1.02 }}
            >
              <div
                className={`${category.color} text-white text-center py-3 px-4 rounded-lg mb-6 font-semibold`}
              >
                {category.title}
              </div>
              <div className='space-y-3'>
                {category.services.map((service, serviceIndex) => (
                  <div
                    key={serviceIndex}
                    className={`${service.color} text-white text-center py-2 px-3 rounded-lg text-sm font-medium hover:shadow-md transition-all duration-200 cursor-pointer`}
                  >
                    {service.name}
                  </div>
                ))}
              </div>
            </motion.div>
          ))}
        </motion.div>

        <motion.div
          className='mt-16 grid md:grid-cols-3 gap-8'
          variants={staggerContainer}
          initial='initial'
          whileInView='animate'
          viewport={{ once: true }}
        >
          {serviceDetails.map((service, index) => (
            <motion.div
              key={index}
              className='bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-blue-100'
              variants={fadeInUp}
              whileHover={{ y: -5 }}
            >
              <div className='w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-400 rounded-xl flex items-center justify-center mb-6'>
                <service.icon className='w-8 h-8 text-white' />
              </div>
              <h3 className='text-xl font-semibold text-gray-800 mb-4'>{service.title}</h3>
              <p className='text-gray-600 leading-relaxed'>{service.description}</p>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  )
}

export default SecurityServicesSection
