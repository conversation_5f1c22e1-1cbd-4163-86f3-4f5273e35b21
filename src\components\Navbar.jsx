import React from 'react'
import { motion } from 'framer-motion'
import { Shield, Menu, X, ChevronDown } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { IconLogo } from './icons'

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = React.useState(false)
  const [isAboutDropdownOpen, setIsAboutDropdownOpen] = React.useState(false)

  const navLinks = [
    { number: '00', href: '#home', title: '首页' },
    { number: '02', href: '#services', title: '安全服务' },
    { number: '03', href: '#products', title: '安全产品' }
  ]

  const aboutDropdownItems = [
    { href: '#company', title: '公司简介' },
    { href: '#honors', title: '荣誉资质' }
  ]

  return (
    <nav className='fixed top-0 w-full bg-white/90 backdrop-blur-md z-50 border-b border-blue-100'>
      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
        <div className='flex justify-between items-center h-16'>
          <motion.div
            className='flex items-center space-x-3'
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <IconLogo />
          </motion.div>

          <div className='hidden md:flex items-center space-x-6'>
            {navLinks.map(link => (
              <a
                key={link.href}
                href={link.href}
                className='text-gray-700 hover:text-blue-600 transition-colors'
              >
                {link.title}
              </a>
            ))}

            {/* 关于我们下拉菜单 */}
            <div
              className='relative'
              onMouseEnter={() => setIsAboutDropdownOpen(true)}
              onMouseLeave={() => setIsAboutDropdownOpen(false)}
            >
              <button className='flex items-center text-gray-700 hover:text-blue-600 transition-colors'>
                关于我们
                <ChevronDown className='w-4 h-4 ml-1' />
              </button>

              {isAboutDropdownOpen && (
                <motion.div
                  className='absolute top-full left-0 mt-2 w-40 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50'
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.2 }}
                >
                  {aboutDropdownItems.map(item => (
                    <a
                      key={item.href}
                      href={item.href}
                      className='block px-4 py-2 text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-colors'
                      onClick={() => setIsAboutDropdownOpen(false)}
                    >
                      {item.title}
                    </a>
                  ))}
                </motion.div>
              )}
            </div>

            <Button className='bg-gradient-to-r from-blue-600 to-cyan-500 hover:from-blue-700 hover:to-cyan-600'>
              联系我们
            </Button>
          </div>

          <button className='md:hidden' onClick={() => setIsMenuOpen(!isMenuOpen)}>
            {isMenuOpen ? <X className='w-6 h-6' /> : <Menu className='w-6 h-6' />}
          </button>
        </div>
      </div>

      {isMenuOpen && (
        <motion.div
          className='md:hidden bg-white border-t border-blue-100'
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <div className='px-4 py-4 space-y-4'>
            {navLinks.map(link => (
              <a
                key={link.href}
                href={link.href}
                className='block text-gray-700 hover:text-blue-600'
                onClick={() => setIsMenuOpen(false)}
              >
                {link.title}
              </a>
            ))}

            {/* 移动端关于我们选项 */}
            <div className='space-y-2'>
              <div className='text-gray-700 font-medium'>关于我们</div>
              {aboutDropdownItems.map(item => (
                <a
                  key={item.href}
                  href={item.href}
                  className='block pl-4 text-gray-600 hover:text-blue-600'
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.title}
                </a>
              ))}
            </div>

            <Button className='w-full bg-gradient-to-r from-blue-600 to-cyan-500'>联系我们</Button>
          </div>
        </motion.div>
      )}
    </nav>
  )
}

export default Navbar
