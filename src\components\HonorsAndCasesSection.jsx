import React from 'react'
import { motion } from 'framer-motion'
import { Award, Users, Briefcase, BookOpen } from 'lucide-react'

const HonorsAndCasesSection = ({ fadeInUp, staggerContainer }) => {
  const honors = [
    '中国信息安全测评中心CNVD二级技术支撑单位',
    '多场网络安全攻防演练第一名',
    '国家信息安全漏洞共享平台CNVD漏洞支撑',
    '为政企客户提供完整的网络安全方案，收获客户好评感谢信'
  ]

  const partners = [
    { category: '政府', names: ['宁波市委网信办', '宁波市公安局多个分局'], icon: Briefcase },
    { category: '企业', names: ['方太集团', '时代互联', '上海三菱'], icon: Users },
    { category: '教育', names: ['宁波职业技术学院'], icon: BookOpen },
    { category: '运营商', names: ['宁波电信', '宁波联通'], icon: Award }
  ]

  return (
    <section id='honors' className='py-20 bg-white'>
      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
        <motion.div
          className='text-center mb-16'
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className='text-4xl font-bold mb-4'>
            <span className='bg-gradient-to-r from-blue-600 to-cyan-500 bg-clip-text text-transparent'>
              荣誉资质与合作案例
            </span>
          </h2>
        </motion.div>

        <div className='grid lg:grid-cols-2 gap-12 items-start'>
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className='space-y-8'
          >
            <div>
              <h3 className='text-2xl font-semibold text-gray-800 mb-6'>荣誉资质</h3>
              <div className='space-y-4'>
                {honors.map((honor, index) => (
                  <motion.div
                    key={index}
                    className='flex items-center p-4 bg-blue-50 rounded-lg shadow-sm hover:shadow-md transition-shadow'
                    variants={fadeInUp}
                    initial='initial'
                    whileInView='animate'
                    viewport={{ once: true, amount: 0.5 }}
                  >
                    <Award className='w-6 h-6 text-blue-500 mr-3 flex-shrink-0' />
                    <p className='text-gray-700'>{honor}</p>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <img
              src='/public/slides/slide-11.png'
              alt='极安科技-荣誉资质与合作案例'
              className='w-full h-auto rounded-2xl shadow-xl'
            />
          </motion.div>
        </div>

        <div>
          <h3 className='text-2xl font-semibold text-gray-800 mb-6 mt-10'>合作案例</h3>
          <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 w-full'>
            {partners.map((partner, index) => (
              <motion.div
                key={index}
                className='bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow border border-blue-100'
                variants={fadeInUp}
                initial='initial'
                whileInView='animate'
                viewport={{ once: true, amount: 0.5 }}
              >
                <div className='flex items-center mb-3'>
                  <partner.icon className='w-7 h-7 text-cyan-500 mr-3' />
                  <h4 className='text-lg font-semibold text-gray-700'>{partner.category}</h4>
                </div>
                <ul className='space-y-1 list-disc list-inside text-gray-600'>
                  {partner.names.map((name, i) => (
                    <li key={i}>{name}</li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}

export default HonorsAndCasesSection
