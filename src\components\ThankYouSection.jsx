import React from 'react'
import { motion } from 'framer-motion'
import { ShieldCheck } from 'lucide-react'

const ThankYouSection = () => {
  return (
    <section className='py-20 relative overflow-hidden bg-gradient-to-br from-blue-500 to-cyan-400'>
      <div className='absolute inset-0 opacity-20'>
        <img
          className='w-full h-full object-cover'
          alt='Abstract background pattern'
          src='/public/images/thank_you_background.jpg'
        />
      </div>
      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10'>
        <motion.div
          className='text-center'
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <img
            src='/public/slides/slide-12.png'
            alt='感谢观看，期待合作'
            className='w-full max-w-4xl mx-auto h-auto rounded-2xl shadow-2xl mb-12'
          />
          <h2 className='text-5xl lg:text-6xl font-bold text-white mb-6'>感谢观看，期待合作</h2>
          <p className='text-xl text-blue-100 mb-10 max-w-2xl mx-auto'>
            极安科技，致力于成为客户信赖的安全伙伴。
          </p>
          <motion.button
            className='bg-white text-blue-600 font-semibold px-10 py-4 rounded-lg shadow-lg hover:bg-gray-100 transition-colors text-lg flex items-center mx-auto'
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <ShieldCheck className='mr-3 w-6 h-6' />
            联系我们获取方案
          </motion.button>
        </motion.div>
      </div>
    </section>
  )
}

export default ThankYouSection
