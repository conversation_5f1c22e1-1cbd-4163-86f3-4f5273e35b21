import React from 'react'
import {
  Shield,
  Globe,
  Lock,
  Eye,
  Users,
  Server,
  Building,
  Zap,
  ChevronRight,
  Menu,
  X,
  Award,
  Briefcase,
  BookOpen,
  ShieldCheck
} from 'lucide-react'
import Navbar from '@/components/Navbar'
import HeroSection from '@/components/HeroSection'
import ContentsSection from '@/components/ContentsSection'
import CompanyIntroductionSection from '@/components/CompanyIntroductionSection'
import SecurityServicesSection from '@/components/SecurityServicesSection'
import SecurityProductsSection from '@/components/SecurityProductsSection'
import HonorsSection from '@/components/HonorsSection'
import ThankYouSection from '@/components/ThankYouSection'
import Footer from '@/components/Footer'

const App = () => {
  const fadeInUp = {
    initial: { opacity: 0, y: 60 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  }

  const staggerContainer = {
    animate: {
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const securityProducts = [
    {
      title: '边界安全',
      icon: Shield,
      items: ['下一代防火墙', '虚拟专用网络(VPN)', 'web应用防火墙(WAF)', '安全隔离网闸']
    },
    {
      title: '应用安全',
      icon: Globe,
      items: ['脆弱性检测扫描', '网页防篡改', '代码安全审计(SAST)', '供应链安全审计(SCA)']
    },
    {
      title: '端点安全',
      icon: Lock,
      items: ['网络安全准入', '终端安全(EDR)', '零信任身份认证', '主机安全']
    },
    {
      title: '安全运营与管理',
      icon: Eye,
      items: [
        '堡垒机',
        '资产管理(CMDB)',
        '日志审计/数据库审计',
        '流量态势感知',
        '攻击面管理与风险评估',
        '漏洞生命周期管理'
      ]
    }
  ]

  const securityServices = [
    {
      title: '边界安全',
      color: 'bg-red-500',
      services: [
        { name: '渗透测试', color: 'bg-cyan-400' },
        { name: '红队评估', color: 'bg-blue-400' },
        { name: '代码安全检测', color: 'bg-cyan-400' },
        { name: '互联网攻击面梳理', color: 'bg-blue-400' },
        { name: '常态化威胁情报监测', color: 'bg-cyan-400' },
        { name: '网站暗链/篡改监测', color: 'bg-blue-400' }
      ]
    },
    {
      title: '内网安全',
      color: 'bg-red-500',
      services: [
        { name: '漏洞扫描', color: 'bg-blue-400' },
        { name: '基线核查', color: 'bg-cyan-400' },
        { name: '安全运维', color: 'bg-blue-400' },
        { name: '资产梳理', color: 'bg-cyan-400' },
        { name: '专项检测评估', color: 'bg-blue-400' },
        { name: '策略梳理与调优', color: 'bg-cyan-400' }
      ]
    },
    {
      title: '安全意识与培训',
      color: 'bg-red-500',
      services: [
        { name: '钓鱼演练', color: 'bg-cyan-400' },
        { name: '应急演练', color: 'bg-blue-400' },
        { name: '安全意识培训', color: 'bg-cyan-400' },
        { name: '安全防护运营培训', color: 'bg-blue-400' },
        { name: '网络安全法律法规', color: 'bg-cyan-400' }
      ]
    },
    {
      title: '安全监管赋能',
      color: 'bg-red-500',
      services: [
        { name: '涉网犯罪打击协助', color: 'bg-blue-400' },
        { name: '安全检查协助', color: 'bg-cyan-400' },
        { name: '电子取证协助', color: 'bg-blue-400' },
        { name: '辖区安全监管赋能', color: 'bg-cyan-400' }
      ]
    },
    {
      title: '安全保障',
      color: 'bg-red-500',
      services: [
        { name: '应急响应', color: 'bg-cyan-400' },
        { name: '重大节点安全保障', color: 'bg-blue-400' },
        { name: '红蓝对抗安全保障', color: 'bg-cyan-400' },
        { name: '暗网威胁情报监测', color: 'bg-blue-400' }
      ]
    },
    {
      title: '安全合规',
      color: 'bg-red-500',
      services: [
        { name: '等级保护辅助', color: 'bg-cyan-400' },
        { name: '移动隐私合规', color: 'bg-blue-400' }
      ]
    }
  ]

  const companyHighlights = [
    {
      number: '01',
      title: '专业理念',
      description:
        '极安科技秉承“安全至上，服务先行”的理念，致力于通过定制化的安全服务和解决方案，提升企业的安全防御能力和应急响应速度，确保客户信息资产的安全。'
    },
    {
      number: '02',
      title: '行业领先',
      description:
        '在瞬息万变的网络安全领域，浙江极安信息科技有限公司以其卓越的服务品质和专业的技术支持，赢得了众多企业的信赖和好评，成为业界领先的网络安全服务提供商之一。'
    },
    {
      number: '03',
      title: '未来展望',
      description:
        '面向未来，极安科技将继续深耕安全领域，不断创新，为客户提供更加安全、可靠、高效的服务，共同构筑数字世界的安全屏障。'
    }
  ]

  return (
    <div className='min-h-screen bg-gradient-to-br from-blue-50 via-cyan-50 to-blue-100'>
      <Navbar />
      <HeroSection />
      <ContentsSection />
      <CompanyIntroductionSection
        companyHighlights={companyHighlights}
        fadeInUp={fadeInUp}
        staggerContainer={staggerContainer}
      />
      <SecurityServicesSection
        securityServices={securityServices}
        fadeInUp={fadeInUp}
        staggerContainer={staggerContainer}
      />
      <SecurityProductsSection
        securityProducts={securityProducts}
        fadeInUp={fadeInUp}
        staggerContainer={staggerContainer}
      />
      <HonorsSection fadeInUp={fadeInUp} staggerContainer={staggerContainer} />
      <ThankYouSection />
      <Footer />
    </div>
  )
}

export default App
