import React from 'react'
import { motion } from 'framer-motion'
import { Shield, Menu, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { IconLogo } from './icons'

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = React.useState(false)

  const navLinks = [
    { number: '00', href: '#home', title: '首页' },
    { number: '01', href: '#company', title: '公司介绍' },
    { number: '02', href: '#services', title: '安全服务' },
    { number: '03', href: '#products', title: '安全产品' },
    { number: '04', href: '#honors', title: '荣誉资质' }
  ]

  return (
    <nav className='fixed top-0 w-full bg-white/90 backdrop-blur-md z-50 border-b border-blue-100'>
      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
        <div className='flex justify-between items-center h-16'>
          <motion.div
            className='flex items-center space-x-3'
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <IconLogo />
          </motion.div>

          <div className='hidden md:flex items-center space-x-6'>
            {navLinks.map(link => (
              <a
                key={link.href}
                href={link.href}
                className='text-gray-700 hover:text-blue-600 transition-colors'
              >
                {link.title}
              </a>
            ))}
            <Button className='bg-gradient-to-r from-blue-600 to-cyan-500 hover:from-blue-700 hover:to-cyan-600'>
              联系我们
            </Button>
          </div>

          <button className='md:hidden' onClick={() => setIsMenuOpen(!isMenuOpen)}>
            {isMenuOpen ? <X className='w-6 h-6' /> : <Menu className='w-6 h-6' />}
          </button>
        </div>
      </div>

      {isMenuOpen && (
        <motion.div
          className='md:hidden bg-white border-t border-blue-100'
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <div className='px-4 py-4 space-y-4'>
            {navLinks.map(link => (
              <a
                key={link.href}
                href={link.href}
                className='block text-gray-700 hover:text-blue-600'
                onClick={() => setIsMenuOpen(false)}
              >
                {link.text}
              </a>
            ))}
            <Button className='w-full bg-gradient-to-r from-blue-600 to-cyan-500'>联系我们</Button>
          </div>
        </motion.div>
      )}
    </nav>
  )
}

export default Navbar
