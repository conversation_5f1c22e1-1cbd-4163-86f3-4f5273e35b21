import React from 'react'
import { motion } from 'framer-motion'
import { ChevronRight } from 'lucide-react'

const ContentsSection = () => {
  const contents = [
    { number: '01', href: '#company', title: '公司介绍', color: 'from-blue-500 to-cyan-400' },
    { number: '02', href: '#services', title: '安全服务体系', color: 'from-cyan-500 to-blue-400' },
    { number: '03', href: '#products', title: '安全产品', color: 'from-blue-600 to-cyan-500' },
    { number: '04', href: '#honors', title: '荣誉资质', color: 'from-cyan-600 to-blue-500' }
  ]

  return (
    <section className='py-16 bg-white/50'>
      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
        <motion.div
          className='text-center mb-16'
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className='text-4xl font-bold mb-4'>
            <span className='bg-gradient-to-r from-blue-600 to-cyan-500 bg-clip-text text-transparent'>
              目录 | CONTENTS
            </span>
          </h2>
        </motion.div>

        <div className='grid lg:grid-cols-2 gap-12 items-center'>
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <img
              src='/public/slides/slide-07.png'
              alt='极安斥候-攻击面管理与风险评估'
              className='w-full h-auto rounded-2xl shadow-xl'
            />
          </motion.div>

          <motion.div
            className='space-y-6'
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            {contents.map((item, index) => (
              <motion.div
                key={item.href}
                className='flex items-center space-x-6 p-6 bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer group'
                whileHover={{ scale: 1.02 }}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div
                  className={`w-16 h-16 bg-gradient-to-br ${item.color} rounded-xl flex items-center justify-center text-white font-bold text-lg shadow-lg`}
                >
                  {item.number}
                </div>
                <div className='flex-1'>
                  <a
                    key={item.href}
                    href={item.href}
                    className='text-xl font-semibold text-gray-800 group-hover:text-blue-600 transition-colors'
                  >
                    {item.title}
                  </a>
                </div>
                <ChevronRight className='w-6 h-6 text-gray-400 group-hover:text-blue-600 transition-colors' />
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default ContentsSection
