import React from 'react'
import { motion } from 'framer-motion'
import { Building, Zap } from 'lucide-react'

const CompanyIntroductionSection = ({ companyHighlights, fadeInUp, staggerContainer }) => {
  return (
    <section id='company' className='py-20 bg-gradient-to-br from-blue-50 to-cyan-50'>
      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
        <motion.div
          className='text-center mb-16'
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className='text-4xl font-bold mb-4'>
            <span className='bg-gradient-to-r from-blue-600 to-cyan-500 bg-clip-text text-transparent'>
              公司介绍
            </span>
          </h2>
        </motion.div>

        <div className='grid lg:grid-cols-2 gap-12 items-center mb-16'>
          <motion.div
            className='space-y-8'
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div>
              <h3 className='text-3xl font-bold text-gray-800 mb-4'>浙江极安信息科技有限公司</h3>
              <p className='text-lg text-gray-600 mb-2'>
                Zhejiang Jian Information Technology Co., LTD
              </p>
            </div>

            <div className='space-y-6'>
              <div className='flex items-start space-x-4'>
                <div className='w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-400 rounded-full flex items-center justify-center flex-shrink-0'>
                  <Building className='w-6 h-6 text-white' />
                </div>
                <div>
                  <p className='text-gray-700 leading-relaxed'>
                    成立于2023年，总部位于浙江省宁波市，是一家专注政企网络安全服务和安全产品的创新型科技企业。
                  </p>
                </div>
              </div>

              <div className='flex items-start space-x-4'>
                <div className='w-12 h-12 bg-gradient-to-br from-cyan-500 to-blue-400 rounded-full flex items-center justify-center flex-shrink-0'>
                  <Zap className='w-6 h-6 text-white' />
                </div>
                <div>
                  <p className='text-gray-700 leading-relaxed'>
                    依托强大的技术研发实力和深厚的行业经验，专注于为政企提供全方位的网络安全解决方案，采用先进的安全技术和方法，结合自主研发的安全产品，帮助客户构建坚固的安全防护体系，有效预防和应对新兴的网络安全挑战。
                  </p>
                </div>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <img
              src='/public/slides/slide-08.png'
              alt='浙江极安信息科技有限公司介绍'
              className='w-full h-auto rounded-2xl shadow-xl'
            />
          </motion.div>
        </div>

        <motion.div
          className='grid md:grid-cols-3 gap-8'
          variants={staggerContainer}
          initial='initial'
          whileInView='animate'
          viewport={{ once: true }}
        >
          {companyHighlights.map((highlight, index) => (
            <motion.div
              key={index}
              className='bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300'
              variants={fadeInUp}
              whileHover={{ y: -5 }}
            >
              <div className='flex items-center mb-6'>
                <div className='w-12 h-12 bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full flex items-center justify-center text-white font-bold text-lg mr-4'>
                  {highlight.number}
                </div>
                <h3 className='text-xl font-semibold text-gray-800'>{highlight.title}</h3>
              </div>
              <p className='text-gray-600 leading-relaxed'>{highlight.description}</p>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  )
}

export default CompanyIntroductionSection
