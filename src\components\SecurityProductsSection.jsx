import React from 'react'
import { motion } from 'framer-motion'

const SecurityProductsSection = ({ securityProducts, fadeInUp, staggerContainer }) => {
  return (
    <section id='products' className='py-20 bg-gradient-to-br from-cyan-50 to-blue-50'>
      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
        <motion.div
          className='text-center mb-16'
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className='text-4xl font-bold mb-4'>
            <span className='bg-gradient-to-r from-blue-600 to-cyan-500 bg-clip-text text-transparent'>
              安全产品
            </span>
          </h2>
        </motion.div>

        <motion.div
          className='grid md:grid-cols-2 xl:grid-cols-4 gap-8'
          variants={staggerContainer}
          initial='initial'
          whileInView='animate'
          viewport={{ once: true }}
        >
          {securityProducts.map((product, index) => (
            <motion.div
              key={index}
              className='bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300'
              variants={fadeInUp}
              whileHover={{ scale: 1.02 }}
            >
              <div className='text-center mb-8'>
                <div className='w-20 h-20 bg-gradient-to-br from-blue-500 to-cyan-400 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg'>
                  <product.icon className='w-10 h-10 text-white' />
                </div>
                <div className='bg-gradient-to-r from-blue-500 to-cyan-400 text-white py-3 px-6 rounded-full font-semibold'>
                  {product.title}
                </div>
              </div>

              <div className='space-y-3'>
                {product.items.map((item, itemIndex) => (
                  <div
                    key={itemIndex}
                    className='flex items-center space-x-3 p-3 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors'
                  >
                    <div className='w-2 h-2 bg-blue-500 rounded-full'></div>
                    <span className='text-gray-700 font-medium'>{item}</span>
                  </div>
                ))}
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  )
}

export default SecurityProductsSection
