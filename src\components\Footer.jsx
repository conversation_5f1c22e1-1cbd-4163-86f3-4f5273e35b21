import React from 'react'
import { Shield } from 'lucide-react'
import { IconLogoSingle } from './icons'

const Footer = () => {
  return (
    <footer className='bg-gray-900 text-white py-16'>
      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
        <div className='grid md:grid-cols-4 gap-8'>
          <div className='space-y-4'>
            <div className='flex items-center space-x-3'>
              <IconLogoSingle />
              <span className='text-xl font-bold'>极安科技</span>
            </div>
            <p className='text-gray-400'>攻防一体守护数字时代安全</p>
          </div>

          <div>
            <span className='text-lg font-semibold mb-4 block'>服务范围</span>
            <ul className='space-y-2 text-gray-400'>
              <li>边界安全</li>
              <li>应用安全</li>
              <li>端点安全</li>
              <li>安全运营</li>
            </ul>
          </div>

          <div>
            <span className='text-lg font-semibold mb-4 block'>联系方式</span>
            <ul className='space-y-2 text-gray-400'>
              <li>地址：浙江省宁波市</li>
              <li>电话：400-xxx-xxxx</li>
              <li>邮箱：<EMAIL></li>
            </ul>
          </div>

          <div>
            <span className='text-lg font-semibold mb-4 block'>关注我们</span>

            <div className='text-center col-span-1'>
              <img
                alt='二维码'
                loading='lazy'
                width='160'
                height='160'
                decoding='async'
                className='transparent'
                src='/images/qr-code.png'
              />
            </div>
          </div>
        </div>

        <div className='border-t border-gray-800 mt-12 pt-8 text-center text-gray-400'>
          <p>&copy; 2023 浙江极安信息科技有限公司. 保留所有权利.</p>
        </div>
      </div>
    </footer>
  )
}

export default Footer
